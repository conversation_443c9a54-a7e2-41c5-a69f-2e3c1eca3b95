/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Tool } from '@langchain/core/tools';
import { FunctionDeclaration } from '@google/genai';
import { z } from 'zod';

// Import core tool types and interfaces
import type {
  Tool as CoreTool,
  ToolResult,
} from '@google/gemini-cli-core';

import type { LangChainConfig } from '../config/config.js';
import { LangChainMCPClient } from '../mcp/mcpClient.js';
import { logger } from '../utils/logger.js';

/**
 * Wrapper that adapts core tools to LangChain tools
 */
export class CoreToolWrapper extends Tool {
  name: string;
  description: string;
  schema = z.object({
    input: z.string().optional().describe('Tool input parameters')
  }).transform((val) => val.input || '');
  private abortController: AbortController;

  constructor(
    public coreTool: CoreTool
  ) {
    super();
    this.name = coreTool.name;
    this.description = coreTool.description;
    this.abortController = new AbortController();
  }

  /**
   * Execute the core tool and return the result as a string
   */
  async _call(input: string | undefined): Promise<string> {
    try {
      // Handle undefined input
      if (input === undefined) {
        throw new Error('Input is required but was undefined');
      }

      // Handle different input types
      let args: Record<string, unknown>;
      
      if (typeof input === 'string') {
        // Try to parse as JSON first
        try {
          args = JSON.parse(input);
        } catch (_parseError) {
          // If not JSON, create a simple object
          logger.debug(`[CoreToolWrapper] Input is not JSON, treating as string: ${input}`);
          args = { input };
        }
      } else {
        throw new Error('Invalid input: must be a string');
      }

      // Validate parameters using core tool validation
      const validationError = this.coreTool.validateToolParams(args);
      if (validationError) {
        throw new Error(`Tool validation failed: ${validationError}`);
      }

      // Execute the core tool with proper abort signal
      const result = await this.coreTool.execute(
        args,
        this.abortController.signal
      );

      // Convert ToolResult to string for LangChain
      return this.formatToolResult(result);
    } catch (error) {
      logger.error(`[CoreToolWrapper] Error executing ${this.name}:`, error);
      
      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return `Tool execution was cancelled: ${this.name}`;
        } else if (error.message.includes('timeout')) {
          return `Tool execution timed out: ${this.name}`;
        } else if (error.message.includes('permission')) {
          return `Permission denied for tool: ${this.name}`;
        } else {
          return `Error executing ${this.name}: ${error.message}`;
        }
      } else {
        return `Error executing ${this.name}: ${String(error)}`;
      }
    }
  }

  /**
   * Cancel the current tool execution
   */
  abort(): void {
    this.abortController.abort();
    // Create a new controller for future executions
    this.abortController = new AbortController();
  }



  /**
   * Format ToolResult for LangChain consumption
   */
  private formatToolResult(result: ToolResult): string {
    if (typeof result.returnDisplay === 'string') {
      return result.returnDisplay;
    } else if (result.returnDisplay && typeof result.returnDisplay === 'object') {
      // Handle FileDiff case
      if ('fileDiff' in result.returnDisplay) {
        return `File modified: ${result.returnDisplay.fileName}\n\n${result.returnDisplay.fileDiff}`;
      }
    }
    
    // Fallback to LLM content
    if (Array.isArray(result.llmContent)) {
      return result.llmContent.map(part => {
        if (typeof part === 'string') return part;
        if (typeof part === 'object' && part !== null && 'text' in part) {
          return (part as { text: string }).text;
        }
        return JSON.stringify(part);
      }).join('\n');
    } else if (typeof result.llmContent === 'string') {
      return result.llmContent;
    } else {
      return JSON.stringify(result.llmContent);
    }
  }
}

/**
 * LangChain tool registry that manages core tools adapted for LangChain
 */
export class LangChainToolRegistry {
  private coreTools: Map<string, CoreTool> = new Map();
  private langChainTools: Map<string, Tool> = new Map();
  private mcpClient: LangChainMCPClient;

  constructor(private config: LangChainConfig) {
    this.mcpClient = new LangChainMCPClient(config);
  }

  /**
   * Register a core tool and create its LangChain wrapper
   */
  registerCoreTool(coreTool: CoreTool): void {
    this.coreTools.set(coreTool.name, coreTool);
    
    const langChainTool = new CoreToolWrapper(coreTool);
    this.langChainTools.set(coreTool.name, langChainTool);
  }

  /**
   * Register a native LangChain tool
   */
  registerLangChainTool(tool: Tool): void {
    this.langChainTools.set(tool.name, tool);
  }

  /**
   * Get all LangChain tools for use with models
   */
  getLangChainTools(): Tool[] {
    return Array.from(this.langChainTools.values());
  }

  /**
   * Get a specific tool by name
   */
  getTool(name: string): Tool | undefined {
    return this.langChainTools.get(name);
  }

  /**
   * Get core tool by name
   */
  getCoreTool(name: string): CoreTool | undefined {
    return this.coreTools.get(name);
  }

  /**
   * List all registered tool names
   */
  getToolNames(): string[] {
    return Array.from(this.langChainTools.keys());
  }

  /**
   * Get function declarations for tools (for Gemini API compatibility)
   */
  getFunctionDeclarations(): FunctionDeclaration[] {
    return Array.from(this.coreTools.values()).map(tool => tool.schema);
  }

  /**
   * Discover and register core tools based on configuration
   */
  async discoverTools(): Promise<void> {
    try {
      // Import core tool classes
      const coreModule = await import('@google/gemini-cli-core');
      const { 
        LSTool,
        ReadFileTool,
        GrepTool,
        GlobTool,
        EditTool,
        WriteFileTool,
        WebFetchTool,
        ReadManyFilesTool,
        ShellTool,
        MemoryTool,
        WebSearchTool,
      } = coreModule;
      const CoreConfig = coreModule.Config;

      // Create a temporary core config to use for tool instantiation
      const tempCoreConfig = new CoreConfig({
        sessionId: 'temp',
        targetDir: this.config.getTargetDir(),
        model: this.config.getModel(),
        cwd: this.config.getWorkingDir(),
        debugMode: this.config.getDebugMode(),
        approvalMode: this.config.getApprovalMode(),
      });

      // Get tool configuration
      const coreTools = this.config.getCoreTools();
      const excludeTools = this.config.getExcludeTools();

      // Define available core tools
      const availableTools = [
        { name: 'list_directory', class: LSTool },
        { name: 'read_file', class: ReadFileTool },
        { name: 'search_file_content', class: GrepTool },
        { name: 'glob', class: GlobTool },
        { name: 'replace', class: EditTool },
        { name: 'write_file', class: WriteFileTool },
        { name: 'web_fetch', class: WebFetchTool },
        { name: 'read_many_files', class: ReadManyFilesTool },
        { name: 'run_shell_command', class: ShellTool },
        { name: 'save_memory', class: MemoryTool },
        { name: 'web_search', class: WebSearchTool },
      ];

      // Filter tools based on configuration
      const toolsToRegister = availableTools.filter(tool => {
        // If coreTools is specified, only include those tools
        if (coreTools && coreTools.length > 0) {
          const isIncluded = coreTools.some(toolName => 
            toolName === tool.name || 
            toolName.startsWith(`${tool.name}(`)
          );
          if (!isIncluded) return false;
        }

        // Exclude specified tools
        if (excludeTools && excludeTools.includes(tool.name)) {
          return false;
        }

        return true;
      });

      // Register filtered tools with enhanced error handling
      let registeredCount = 0;
      const failedTools: string[] = [];

      for (const toolDef of toolsToRegister) {
        try {
          const coreToolInstance = new toolDef.class(tempCoreConfig);
          this.registerCoreTool(coreToolInstance);
          registeredCount++;
        } catch (error) {
          logger.warning(`Failed to register tool ${toolDef.name}:`, error);
          failedTools.push(toolDef.name);
        }
      }

      logger.info(`Discovered and registered ${registeredCount} core tools`);
      if (failedTools.length > 0) {
        logger.warning(`Failed to register ${failedTools.length} tools:`, failedTools);
      }

      // Discover and register MCP tools
      await this.discoverMCPTools();
      
    } catch (error) {
      logger.error('Failed to discover tools:', error);
      
      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('module')) {
          throw new Error('Failed to load core tools module. Please check your installation.');
        } else if (error.message.includes('config')) {
          throw new Error('Invalid tool configuration. Please check your settings.');
        } else {
          throw new Error(`Tool discovery failed: ${error.message}`);
        }
      } else {
        throw new Error(`Tool discovery failed: ${String(error)}`);
      }
    }
  }

  /**
   * Discover and register MCP tools
   */
  async discoverMCPTools(): Promise<void> {
    try {
      if (!this.mcpClient.isMCPConfigured()) {
        logger.debug('[LangChainToolRegistry] No MCP servers configured, skipping MCP tool discovery');
        return;
      }

      logger.debug('[LangChainToolRegistry] Starting MCP tool discovery...');
      const mcpTools = await this.mcpClient.discoverMCPTools();
      
      // Register discovered MCP tools
      for (const tool of mcpTools) {
        this.registerLangChainTool(tool);
        logger.debug(`[LangChainToolRegistry] Registered MCP tool: ${tool.name}`);
      }

      logger.info(`[LangChainToolRegistry] Successfully registered ${mcpTools.length} MCP tools`);
    } catch (error) {
      logger.error('[LangChainToolRegistry] Failed to discover MCP tools:', error);
      // Don't throw error for MCP discovery failure - continue with core tools
    }
  }

  /**
   * Get MCP client for advanced MCP operations
   */
  getMCPClient(): LangChainMCPClient {
    return this.mcpClient;
  }

  /**
   * Clear all registered tools
   */
  clear(): void {
    this.coreTools.clear();
    this.langChainTools.clear();
    this.mcpClient.clearDiscoveredTools();
  }

  /**
   * Get tool count
   */
  getToolCount(): number {
    return this.langChainTools.size;
  }
}