/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Factory functions to create LangChain wrappers for core tools
 * This module helps bridge core tools with the LangChain tool system
 */

import type {
  LSTool,
  ReadFileTool,
  GrepTool,
  GlobTool,
  EditTool,
  WriteFileTool,
  WebFetchTool,
  ReadManyFilesTool,
  ShellTool,
  MemoryTool,
  WebSearchTool,
} from '@google/gemini-cli-core';

import { CoreToolWrapper } from './toolRegistry.js';
import type { LangChainConfig } from '../config/config.js';

/**
 * Create LangChain wrappers for all core tools
 */
export function createCoreToolWrappers(
  _config: LangChainConfig,
  coreToolInstances: {
    lsTool: LSTool;
    readFileTool: ReadFileTool;
    grepTool: GrepTool;
    globTool: GlobTool;
    editTool: EditTool;
    writeFileTool: WriteFileTool;
    webFetchTool: WebFetchTool;
    readManyFilesTool: ReadManyFilesTool;
    shellTool: ShellTool;
    memoryTool: MemoryTool;
    webSearchTool: WebSearchTool;
  }
): CoreToolWrapper[] {
  return [
    new CoreToolWrapper(coreToolInstances.lsTool),
    new CoreToolWrapper(coreToolInstances.readFileTool),
    new CoreToolWrapper(coreToolInstances.grepTool),
    new CoreToolWrapper(coreToolInstances.globTool),
    new CoreToolWrapper(coreToolInstances.editTool),
    new CoreToolWrapper(coreToolInstances.writeFileTool),
    new CoreToolWrapper(coreToolInstances.webFetchTool),
    new CoreToolWrapper(coreToolInstances.readManyFilesTool),
    new CoreToolWrapper(coreToolInstances.shellTool),
    new CoreToolWrapper(coreToolInstances.memoryTool),
    new CoreToolWrapper(coreToolInstances.webSearchTool),
  ];
}

/**
 * Get the default set of enabled core tools
 */
export function getDefaultCoreTools(): string[] {
  return [
    'list_directory',      // LSTool
    'read_file',          // ReadFileTool
    'search_file_content', // GrepTool
    'glob',               // GlobTool
    'replace',            // EditTool
    'write_file',         // WriteFileTool
    'web_fetch',          // WebFetchTool
    'read_many_files',    // ReadManyFilesTool
    'run_shell_command',  // ShellTool
    'save_memory',        // MemoryTool
    'web_search',         // WebSearchTool
  ];
}

/**
 * Filter tools based on core tools and exclude tools configuration
 */
export function filterTools(
  toolWrappers: CoreToolWrapper[],
  coreTools?: string[],
  excludeTools?: string[]
): CoreToolWrapper[] {
  let filtered = toolWrappers;

  // If coreTools is specified, only include those tools
  if (coreTools && coreTools.length > 0) {
    filtered = filtered.filter(wrapper => 
      coreTools.some(toolName => 
        toolName === wrapper.name || 
        toolName === wrapper.coreTool.constructor.name ||
        toolName.startsWith(`${wrapper.name}(`) ||
        toolName.startsWith(`${wrapper.coreTool.constructor.name}(`)
      )
    );
  }

  // Exclude specified tools
  if (excludeTools && excludeTools.length > 0) {
    filtered = filtered.filter(wrapper => 
      !excludeTools.includes(wrapper.name) && 
      !excludeTools.includes(wrapper.coreTool.constructor.name)
    );
  }

  return filtered;
}

/**
 * Create tool descriptions for logging/debugging
 */
export function getToolDescriptions(toolWrappers: CoreToolWrapper[]): Record<string, string> {
  const descriptions: Record<string, string> = {};
  
  for (const wrapper of toolWrappers) {
    descriptions[wrapper.name] = wrapper.description;
  }
  
  return descriptions;
}

/**
 * Validate tool configuration
 */
export function validateToolConfiguration(
  coreTools?: string[],
  excludeTools?: string[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const validToolNames = getDefaultCoreTools();
  
  // Validate core tools
  if (coreTools) {
    for (const toolName of coreTools) {
      // Extract base name if it has parameters (e.g., "tool_name(param=value)")
      const baseName = toolName.split('(')[0];
      if (!validToolNames.includes(baseName)) {
        errors.push(`Invalid core tool: ${toolName}`);
      }
    }
  }
  
  // Validate exclude tools
  if (excludeTools) {
    for (const toolName of excludeTools) {
      if (!validToolNames.includes(toolName)) {
        errors.push(`Invalid exclude tool: ${toolName}`);
      }
    }
  }
  
  // Check for conflicts (tool in both core and exclude)
  if (coreTools && excludeTools) {
    const conflicts = coreTools.filter(tool => excludeTools.includes(tool));
    if (conflicts.length > 0) {
      errors.push(`Tools specified in both core and exclude: ${conflicts.join(', ')}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}