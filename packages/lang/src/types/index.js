/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
export { AuthType } from '@google/gemini-cli-core';
// Extended AuthType to support OpenAI compatible providers  
export const ExtendedAuthType = {
    USE_GEMINI: 'USE_GEMINI',
    USE_VERTEX_AI: 'USE_VERTEX_AI',
    LOGIN_WITH_GOOGLE: 'LOGIN_WITH_GOOGLE',
    CLOUD_SHELL: 'CLOUD_SHELL',
    USE_OPENAI_COMPATIBLE: 'USE_OPENAI_COMPATIBLE',
    USE_ANTHROPIC: 'USE_ANTHROPIC',
};
