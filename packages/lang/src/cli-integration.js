/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { createLangChainGeminiCLI } from './index.js';
import logger from './utils/logger.js';
/**
 * Check if LangChain implementation should be used
 */
export function shouldUseLangChain() {
    return process.env.GEMINI_CLI_USE_LANGCHAIN === 'true';
}
/**
 * Create a CLI instance based on environment settings
 * This function can be used as a drop-in replacement for the core Config class
 */
export async function createCLIInstance(params) {
    if (shouldUseLangChain()) {
        logger.info('[CLI] Using LangChain implementation');
        return await createLangChainGeminiCLI(params);
    }
    else {
        // Fallback to core implementation
        const { Config } = await import('@google/gemini-cli-core');
        logger.info('[CLI] Using core implementation');
        return new Config(params);
    }
}
/**
 * Environment variable check utility
 */
export function getLangChainSettings() {
    return {
        enabled: shouldUseLangChain(),
        debug: process.env.GEMINI_CLI_LANGCHAIN_DEBUG === 'true',
        modelProvider: process.env.GEMINI_CLI_LANGCHAIN_PROVIDER || 'google',
    };
}
/**
 * Helper to set up LangChain environment
 */
export function setupLangChainEnvironment() {
    if (shouldUseLangChain()) {
        // Set debug level if enabled
        if (process.env.GEMINI_CLI_LANGCHAIN_DEBUG === 'true') {
            process.env.DEBUG = process.env.DEBUG
                ? `${process.env.DEBUG},langchain*`
                : 'langchain*';
        }
        // Additional LangChain-specific setup
        logger.success('[CLI] LangChain environment configured');
    }
}
