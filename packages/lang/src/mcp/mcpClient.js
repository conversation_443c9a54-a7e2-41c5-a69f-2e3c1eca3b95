/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Tool } from '@langchain/core/tools';
// Import core MCP functionality
import { discoverMcpTools, DiscoveredMCPTool, ToolRegistry as CoreToolRegistry, } from '@google/gemini-cli-core';
import logger from '../utils/logger.js';
/**
 * Wrapper that adapts MCP tools from core package to LangChain tools
 */
export class MCPToolAdapter extends Tool {
    mcpTool;
    config;
    name;
    description;
    constructor(mcpTool, config) {
        super();
        this.mcpTool = mcpTool;
        this.config = config;
        this.name = mcpTool.name;
        this.description = mcpTool.description;
    }
    async _call(input) {
        try {
            // Parse input if it's a JSON string
            let args;
            try {
                args = JSON.parse(input);
            }
            catch {
                // If not JSON, treat as simple string input
                args = { input };
            }
            // Execute the MCP tool through core implementation
            const result = await this.mcpTool.execute(args);
            // Format the result for LangChain consumption
            if (typeof result.returnDisplay === 'string') {
                return result.returnDisplay;
            }
            else if (result.returnDisplay && typeof result.returnDisplay === 'object') {
                // Handle complex return types
                return JSON.stringify(result.returnDisplay);
            }
            return result.returnDisplay ? String(result.returnDisplay) : 'No output';
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            return `Error executing MCP tool ${this.name}: ${errorMessage}`;
        }
    }
}
/**
 * LangChain-compatible MCP client that discovers and adapts MCP tools
 */
export class LangChainMCPClient {
    config;
    discoveredTools = new Map();
    constructor(config) {
        this.config = config;
    }
    /**
     * Discover tools from all configured MCP servers
     */
    async discoverMCPTools() {
        try {
            const mcpServers = this.config.getMcpServers() ?? {};
            const mcpServerCommand = this.config.getMcpServerCommand();
            if (Object.keys(mcpServers).length === 0 && !mcpServerCommand) {
                logger.debug('[LangChainMCPClient] No MCP servers configured');
                return [];
            }
            // Create a temporary core tool registry to collect discovered tools
            // Create a minimal config object that satisfies core Config interface
            const tempCoreConfig = {
                sessionId: 'temp-mcp-discovery',
                targetDir: this.config.getTargetDir(),
                model: this.config.getModel(),
                cwd: this.config.getWorkingDir(),
                debugMode: this.config.getDebugMode(),
                approvalMode: this.config.getApprovalMode(),
                getUserMemory: () => this.config.getUserMemory(),
                setUserMemory: (memory) => this.config.setUserMemory(memory),
                getFileFilteringOptions: () => this.config.getFileFilteringOptions(),
                getMcpServers: () => this.config.getMcpServers(),
                getMcpServerCommand: () => this.config.getMcpServerCommand(),
                getUsageStatisticsEnabled: () => this.config.getUsageStatisticsEnabled(),
            };
            const tempCoreRegistry = new CoreToolRegistry(tempCoreConfig);
            // Use core package's MCP discovery logic
            await discoverMcpTools(mcpServers, mcpServerCommand, tempCoreRegistry, this.config.getDebugMode());
            // Convert discovered MCP tools to LangChain tools
            const langChainTools = [];
            const coreTools = await tempCoreRegistry.getAllTools();
            let discoveredCount = 0;
            const failedTools = [];
            for (const coreTool of coreTools) {
                if (coreTool instanceof DiscoveredMCPTool) {
                    try {
                        const langChainTool = new MCPToolAdapter(coreTool, this.config);
                        this.discoveredTools.set(coreTool.name, langChainTool);
                        langChainTools.push(langChainTool);
                        discoveredCount++;
                        logger.debug(`[LangChainMCPClient] Discovered MCP tool: ${coreTool.name}`);
                    }
                    catch (error) {
                        logger.warning(`[LangChainMCPClient] Failed to adapt MCP tool ${coreTool.name}:`, error);
                        failedTools.push(coreTool.name);
                    }
                }
            }
            logger.success(`[LangChainMCPClient] Successfully discovered ${discoveredCount} MCP tools`);
            if (failedTools.length > 0) {
                logger.warning(`[LangChainMCPClient] Failed to adapt ${failedTools.length} MCP tools:`, failedTools);
            }
            return langChainTools;
        }
        catch (error) {
            logger.error('[LangChainMCPClient] Failed to discover MCP tools:', error);
            // Provide more specific error messages
            if (error instanceof Error) {
                if (error.message.includes('connection')) {
                    throw new Error('Failed to connect to MCP servers. Please check your MCP configuration.');
                }
                else if (error.message.includes('timeout')) {
                    throw new Error('MCP server discovery timed out. Please try again.');
                }
                else if (error.message.includes('permission')) {
                    throw new Error('Permission denied accessing MCP servers. Please check your permissions.');
                }
                else {
                    throw new Error(`MCP tool discovery failed: ${error.message}`);
                }
            }
            else {
                throw new Error(`MCP tool discovery failed: ${String(error)}`);
            }
        }
    }
    /**
     * Get a specific discovered MCP tool by name
     */
    getMCPTool(toolName) {
        return this.discoveredTools.get(toolName);
    }
    /**
     * Get all discovered MCP tools
     */
    getAllMCPTools() {
        return Array.from(this.discoveredTools.values());
    }
    /**
     * Check if MCP is configured
     */
    isMCPConfigured() {
        const mcpServers = this.config.getMcpServers() ?? {};
        const mcpServerCommand = this.config.getMcpServerCommand();
        return Object.keys(mcpServers).length > 0 || !!mcpServerCommand;
    }
    /**
     * Clear all discovered tools (useful for re-discovery)
     */
    clearDiscoveredTools() {
        this.discoveredTools.clear();
    }
}
