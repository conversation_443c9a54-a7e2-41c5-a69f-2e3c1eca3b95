/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { AIMessage } from '@langchain/core/messages';
/**
 * LangChain version of loop detection service with enhanced capabilities
 * Provides multiple layers of protection against infinite loops
 */
export class LangChainLoopDetectionService {
    toolCallHistory = [];
    contentHistory = [];
    config;
    // Thresholds for different types of loop detection
    TOOL_CALL_THRESHOLD = 5; // Same tool called 5+ times
    CONTENT_REPEAT_THRESHOLD = 10; // Same content repeated 10+ times
    TIME_WINDOW_MS = 30000; // 30 seconds window for pattern detection
    MAX_HISTORY_SIZE = 100; // Limit memory usage
    constructor(config) {
        this.config = config;
    }
    /**
     * Check if current state indicates a potential loop
     */
    detectLoop(messages) {
        const now = Date.now();
        this.cleanupOldHistory(now);
        // Extract latest message for analysis
        const latestMessage = messages[messages.length - 1];
        // Check for tool call loops
        if (latestMessage instanceof AIMessage && latestMessage.tool_calls?.length) {
            const toolCallLoop = this.detectToolCallLoop(latestMessage, now);
            if (toolCallLoop.isLoop) {
                return toolCallLoop;
            }
        }
        // Check for content repetition loops
        if (latestMessage.content && typeof latestMessage.content === 'string') {
            const contentLoop = this.detectContentLoop(latestMessage.content, now);
            if (contentLoop.isLoop) {
                return contentLoop;
            }
        }
        // Check for oscillating patterns
        const oscillationLoop = this.detectOscillationPattern(messages);
        if (oscillationLoop.isLoop) {
            return oscillationLoop;
        }
        return {
            isLoop: false,
            loopType: 'none',
            reason: 'No loop detected',
            confidence: 0
        };
    }
    /**
     * Detect tool call loops - same tool called repeatedly
     */
    detectToolCallLoop(message, now) {
        if (!message.tool_calls?.length) {
            return { isLoop: false, loopType: 'none', reason: '', confidence: 0 };
        }
        const toolCall = message.tool_calls[0];
        const pattern = {
            toolName: toolCall.name,
            args: toolCall.args || {},
            timestamp: now
        };
        this.toolCallHistory.push(pattern);
        // Count recent calls to the same tool with similar arguments
        const recentCalls = this.toolCallHistory.filter(call => call.toolName === pattern.toolName &&
            now - call.timestamp < this.TIME_WINDOW_MS);
        if (recentCalls.length >= this.TOOL_CALL_THRESHOLD) {
            // Check if arguments are very similar (indicating a loop)
            const similarCallsCount = recentCalls.filter(call => this.areSimilarArgs(call.args, pattern.args)).length;
            if (similarCallsCount >= this.TOOL_CALL_THRESHOLD) {
                const confidence = Math.min(similarCallsCount / this.TOOL_CALL_THRESHOLD, 1.0);
                return {
                    isLoop: true,
                    loopType: 'tool_call',
                    reason: `Tool '${pattern.toolName}' called ${similarCallsCount} times with similar arguments in ${this.TIME_WINDOW_MS / 1000}s`,
                    confidence
                };
            }
        }
        return { isLoop: false, loopType: 'none', reason: '', confidence: 0 };
    }
    /**
     * Detect content repetition loops
     */
    detectContentLoop(content, now) {
        const contentHash = this.hashContent(content);
        const pattern = {
            content: content.slice(0, 200), // Store first 200 chars for debugging
            timestamp: now,
            hash: contentHash
        };
        this.contentHistory.push(pattern);
        // Count recent identical content
        const recentContent = this.contentHistory.filter(item => item.hash === contentHash &&
            now - item.timestamp < this.TIME_WINDOW_MS);
        if (recentContent.length >= this.CONTENT_REPEAT_THRESHOLD) {
            const confidence = Math.min(recentContent.length / this.CONTENT_REPEAT_THRESHOLD, 1.0);
            return {
                isLoop: true,
                loopType: 'content_repeat',
                reason: `Same content repeated ${recentContent.length} times in ${this.TIME_WINDOW_MS / 1000}s`,
                confidence
            };
        }
        return { isLoop: false, loopType: 'none', reason: '', confidence: 0 };
    }
    /**
     * Detect oscillating patterns in conversation (A->B->A->B...)
     */
    detectOscillationPattern(messages) {
        if (messages.length < 6) {
            return { isLoop: false, loopType: 'none', reason: '', confidence: 0 };
        }
        // Look for ABAB pattern in last 6 messages
        const recent = messages.slice(-6);
        const contentHashes = recent.map(msg => typeof msg.content === 'string' ? this.hashContent(msg.content) : '');
        // Check for ABABAB pattern
        if (contentHashes.length >= 6) {
            const [a1, b1, a2, b2, a3, b3] = contentHashes;
            if (a1 === a2 && a2 === a3 && b1 === b2 && b2 === b3 && a1 !== b1) {
                return {
                    isLoop: true,
                    loopType: 'content_repeat',
                    reason: 'Detected oscillating conversation pattern (A-B-A-B-A-B)',
                    confidence: 0.9
                };
            }
        }
        return { isLoop: false, loopType: 'none', reason: '', confidence: 0 };
    }
    /**
     * Check if two argument objects are similar enough to indicate a loop
     */
    areSimilarArgs(args1, args2) {
        const keys1 = Object.keys(args1);
        const keys2 = Object.keys(args2);
        // Different number of keys = not similar
        if (keys1.length !== keys2.length) {
            return false;
        }
        // Check if all values are identical or very similar
        let similarCount = 0;
        for (const key of keys1) {
            if (!(key in args2)) {
                return false;
            }
            const val1 = args1[key];
            const val2 = args2[key];
            if (val1 === val2) {
                similarCount++;
            }
            else if (typeof val1 === 'string' && typeof val2 === 'string') {
                // Check string similarity
                const similarity = this.getStringSimilarity(val1, val2);
                if (similarity > 0.8) {
                    similarCount++;
                }
            }
        }
        // Consider similar if 80% of arguments are similar
        return similarCount / keys1.length > 0.8;
    }
    /**
     * Calculate similarity between two strings (0-1)
     */
    getStringSimilarity(str1, str2) {
        if (str1 === str2)
            return 1.0;
        if (str1.length === 0 || str2.length === 0)
            return 0.0;
        // Simple Levenshtein distance approximation
        const maxLen = Math.max(str1.length, str2.length);
        const distance = this.levenshteinDistance(str1, str2);
        return 1 - (distance / maxLen);
    }
    /**
     * Calculate Levenshtein distance between two strings
     */
    levenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        for (let i = 0; i <= str1.length; i++) {
            matrix[0][i] = i;
        }
        for (let j = 0; j <= str2.length; j++) {
            matrix[j][0] = j;
        }
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(matrix[j][i - 1] + 1, // deletion
                matrix[j - 1][i] + 1, // insertion
                matrix[j - 1][i - 1] + indicator // substitution
                );
            }
        }
        return matrix[str2.length][str1.length];
    }
    /**
     * Create a hash of content for comparison
     */
    hashContent(content) {
        // Simple hash function for content comparison
        let hash = 0;
        if (content.length === 0)
            return hash.toString();
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }
    /**
     * Clean up old history to prevent memory leaks
     */
    cleanupOldHistory(now) {
        // Remove old entries beyond time window
        this.toolCallHistory = this.toolCallHistory.filter(call => now - call.timestamp < this.TIME_WINDOW_MS);
        this.contentHistory = this.contentHistory.filter(content => now - content.timestamp < this.TIME_WINDOW_MS);
        // Limit history size
        if (this.toolCallHistory.length > this.MAX_HISTORY_SIZE) {
            this.toolCallHistory = this.toolCallHistory.slice(-this.MAX_HISTORY_SIZE);
        }
        if (this.contentHistory.length > this.MAX_HISTORY_SIZE) {
            this.contentHistory = this.contentHistory.slice(-this.MAX_HISTORY_SIZE);
        }
    }
    /**
     * Reset detection state (useful for new conversations)
     */
    reset() {
        this.toolCallHistory = [];
        this.contentHistory = [];
    }
    /**
     * Get current detection statistics
     */
    getStats() {
        return {
            toolCallHistorySize: this.toolCallHistory.length,
            contentHistorySize: this.contentHistory.length,
            thresholds: {
                toolCall: this.TOOL_CALL_THRESHOLD,
                contentRepeat: this.CONTENT_REPEAT_THRESHOLD,
                timeWindowMs: this.TIME_WINDOW_MS,
            }
        };
    }
}
