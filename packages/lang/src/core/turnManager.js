/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import logger from '../utils/logger.js';
/**
 * Advanced turn management for LangGraph conversations
 * Provides fine-grained control over conversation sessions and turns
 */
export class LangChainTurnManager {
    turns = new Map();
    activeTurns = new Map();
    config;
    checkpointer;
    constructor(config = {}, checkpointer) {
        this.config = {
            maxTurnsPerSession: config.maxTurnsPerSession || 100,
            turnTimeoutMs: config.turnTimeoutMs || 300000, // 5 minutes
            enableTurnLogging: config.enableTurnLogging !== false,
            enableTurnCompression: config.enableTurnCompression !== false,
            compressionThreshold: config.compressionThreshold || 50,
        };
        this.checkpointer = checkpointer;
    }
    /**
     * Start a new conversation turn
     */
    async startTurn(sessionId, userMessage, context) {
        const turnId = this.generateTurnId(sessionId);
        const turn = {
            id: turnId,
            sessionId,
            timestamp: new Date(),
            userMessage,
            metadata: {
                context,
            },
            status: 'pending',
        };
        // Store the active turn
        this.activeTurns.set(turnId, turn);
        // Add to session history
        if (!this.turns.has(sessionId)) {
            this.turns.set(sessionId, []);
        }
        const sessionTurns = this.turns.get(sessionId);
        sessionTurns.push(turn);
        // Check if we need to compress old turns
        if (sessionTurns.length > this.config.compressionThreshold) {
            await this.compressTurns(sessionId);
        }
        // Set timeout for turn
        setTimeout(() => {
            if (this.activeTurns.has(turnId) && this.activeTurns.get(turnId)?.status === 'pending') {
                this.timeoutTurn(turnId);
            }
        }, this.config.turnTimeoutMs);
        if (this.config.enableTurnLogging) {
            logger.info(`[TurnManager] Started turn ${turnId} for session ${sessionId}`);
        }
        return turn;
    }
    /**
     * Complete a conversation turn with the assistant's response
     */
    async completeTurn(turnId, assistantMessage, metadata) {
        const turn = this.activeTurns.get(turnId);
        if (!turn) {
            throw new Error(`Turn ${turnId} not found or already completed`);
        }
        const completedTurn = {
            ...turn,
            assistantMessage,
            metadata: {
                ...turn.metadata,
                ...metadata,
                duration: Date.now() - turn.timestamp.getTime(),
            },
            status: 'completed',
        };
        // Update in session history
        const sessionTurns = this.turns.get(turn.sessionId);
        if (sessionTurns) {
            const index = sessionTurns.findIndex(t => t.id === turnId);
            if (index !== -1) {
                sessionTurns[index] = completedTurn;
            }
        }
        // Remove from active turns
        this.activeTurns.delete(turnId);
        // Persist to checkpointer if available
        if (this.checkpointer) {
            await this.persistTurn(completedTurn);
        }
        if (this.config.enableTurnLogging) {
            logger.success(`[TurnManager] Completed turn ${turnId} in ${completedTurn.metadata.duration}ms`);
        }
        return completedTurn;
    }
    /**
     * Fail a conversation turn
     */
    async failTurn(turnId, error) {
        const turn = this.activeTurns.get(turnId);
        if (!turn) {
            throw new Error(`Turn ${turnId} not found`);
        }
        const failedTurn = {
            ...turn,
            metadata: {
                ...turn.metadata,
                duration: Date.now() - turn.timestamp.getTime(),
                error: error.message,
            },
            status: 'failed',
        };
        // Update in session history
        const sessionTurns = this.turns.get(turn.sessionId);
        if (sessionTurns) {
            const index = sessionTurns.findIndex(t => t.id === turnId);
            if (index !== -1) {
                sessionTurns[index] = failedTurn;
            }
        }
        // Remove from active turns
        this.activeTurns.delete(turnId);
        if (this.config.enableTurnLogging) {
            logger.error(`[TurnManager] Failed turn ${turnId}: ${error.message}`);
        }
        return failedTurn;
    }
    /**
     * Get all turns for a session
     */
    getSessionTurns(sessionId) {
        return this.turns.get(sessionId) || [];
    }
    /**
     * Get active turn for a session (if any)
     */
    getActiveTurn(sessionId) {
        for (const turn of this.activeTurns.values()) {
            if (turn.sessionId === sessionId) {
                return turn;
            }
        }
        return undefined;
    }
    /**
     * Get recent messages for context (excluding current turn)
     */
    getRecentMessages(sessionId, count = 10) {
        const sessionTurns = this.getSessionTurns(sessionId);
        const completedTurns = sessionTurns.filter(t => t.status === 'completed');
        // Get the last N completed turns
        const recentTurns = completedTurns.slice(-count);
        const messages = [];
        for (const turn of recentTurns) {
            messages.push(turn.userMessage);
            if (turn.assistantMessage) {
                messages.push(turn.assistantMessage);
            }
        }
        return messages;
    }
    /**
     * Add feedback to a turn
     */
    async addTurnFeedback(turnId, feedback) {
        const sessionTurns = Array.from(this.turns.values()).flat();
        const turn = sessionTurns.find(t => t.id === turnId);
        if (turn) {
            turn.metadata.feedback = feedback;
            // Persist feedback if checkpointer available
            if (this.checkpointer) {
                await this.persistTurn(turn);
            }
            if (this.config.enableTurnLogging) {
                logger.info(`[TurnManager] Added ${feedback} feedback to turn ${turnId}`);
            }
        }
    }
    /**
     * Get session statistics
     */
    getSessionStats(sessionId) {
        const sessionTurns = this.getSessionTurns(sessionId);
        const completedTurns = sessionTurns.filter(t => t.status === 'completed');
        const failedTurns = sessionTurns.filter(t => t.status === 'failed');
        const totalDuration = completedTurns.reduce((sum, turn) => sum + (turn.metadata.duration || 0), 0);
        const totalTokenUsage = completedTurns.reduce((sum, turn) => sum + (turn.metadata.tokenUsage?.total || 0), 0);
        return {
            totalTurns: sessionTurns.length,
            completedTurns: completedTurns.length,
            failedTurns: failedTurns.length,
            averageDuration: completedTurns.length > 0 ? totalDuration / completedTurns.length : 0,
            totalTokenUsage,
        };
    }
    /**
     * Clear old sessions to free memory
     */
    async clearOldSessions(maxAge = 24 * 60 * 60 * 1000) {
        const now = Date.now();
        const sessionsToDelete = [];
        for (const [sessionId, turns] of this.turns.entries()) {
            const lastTurn = turns[turns.length - 1];
            if (lastTurn && (now - lastTurn.timestamp.getTime()) > maxAge) {
                sessionsToDelete.push(sessionId);
            }
        }
        for (const sessionId of sessionsToDelete) {
            this.turns.delete(sessionId);
            if (this.config.enableTurnLogging) {
                logger.info(`[TurnManager] Cleared old session ${sessionId}`);
            }
        }
    }
    /**
     * Generate a unique turn ID
     */
    generateTurnId(sessionId) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `${sessionId}_${timestamp}_${random}`;
    }
    /**
     * Handle turn timeout
     */
    async timeoutTurn(turnId) {
        const turn = this.activeTurns.get(turnId);
        if (turn) {
            await this.failTurn(turnId, new Error('Turn timeout'));
        }
    }
    /**
     * Compress old turns in a session to save memory
     */
    async compressTurns(sessionId) {
        if (!this.config.enableTurnCompression) {
            return;
        }
        const sessionTurns = this.turns.get(sessionId);
        if (!sessionTurns || sessionTurns.length <= this.config.compressionThreshold) {
            return;
        }
        // Keep recent turns, compress older ones
        const recentTurns = sessionTurns.slice(-this.config.compressionThreshold);
        const oldTurns = sessionTurns.slice(0, -this.config.compressionThreshold);
        // Create a compressed summary of old turns
        const compressedSummary = {
            id: `compressed_${sessionId}_${Date.now()}`,
            sessionId,
            timestamp: oldTurns[0]?.timestamp || new Date(),
            userMessage: new HumanMessage({
                content: `[Compressed summary of ${oldTurns.length} previous turns]`,
            }),
            assistantMessage: new AIMessage({
                content: `Summary of previous conversation: ${oldTurns.length} turns completed.`,
            }),
            metadata: {
                compressed: true,
                originalTurnCount: oldTurns.length,
                compressionTimestamp: Date.now(),
            },
            status: 'completed',
        };
        // Replace old turns with compressed summary
        this.turns.set(sessionId, [compressedSummary, ...recentTurns]);
        if (this.config.enableTurnLogging) {
            logger.info(`[TurnManager] Compressed ${oldTurns.length} turns for session ${sessionId}`);
        }
    }
    /**
     * Persist turn to checkpointer
     */
    async persistTurn(turn) {
        if (!this.checkpointer) {
            return;
        }
        try {
            // Implementation would depend on the specific checkpointer interface
            // This is a placeholder for the persistence logic
            if (this.config.enableTurnLogging) {
                logger.debug(`[TurnManager] Persisted turn ${turn.id} to checkpointer`);
            }
        }
        catch (error) {
            logger.error(`[TurnManager] Failed to persist turn ${turn.id}:`, error);
        }
    }
}
