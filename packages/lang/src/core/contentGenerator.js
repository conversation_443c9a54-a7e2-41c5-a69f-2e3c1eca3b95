/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages';
/**
 * LangChain-based content generator that implements the same interface
 * as the core ContentGenerator but uses LangChain models internally.
 */
export class LangChainContentGenerator {
    chatModel;
    embeddings;
    tools;
    constructor(chatModel, embeddings, tools) {
        this.chatModel = chatModel;
        this.embeddings = embeddings;
        this.tools = tools;
    }
    /**
     * Generate content using LangChain chat model
     */
    async generateContent(request) {
        try {
            const messages = this.convertContentsToMessages(request.contents || []);
            // Add system instruction if provided
            if (request.systemInstruction) {
                const systemContent = this.extractTextFromContent(request.systemInstruction);
                messages.unshift(new SystemMessage(systemContent));
            }
            // Configure tools if provided
            const modelWithTools = request.tools && request.tools.length > 0 && this.chatModel.bindTools
                ? this.chatModel.bindTools(this.tools)
                : this.chatModel;
            // Generate response
            const result = await modelWithTools.invoke(messages);
            // Convert LangChain response to our format
            return this.convertToGenerateContentResponse(result, request);
        }
        catch (error) {
            throw new Error(`Content generation failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Generate streaming content using LangChain chat model
     */
    async generateContentStream(request) {
        const messages = this.convertContentsToMessages(request.contents || []);
        // Add system instruction if provided
        if (request.systemInstruction) {
            const systemContent = this.extractTextFromContent(request.systemInstruction);
            messages.unshift(new SystemMessage(systemContent));
        }
        // Configure tools if provided
        const modelWithTools = request.tools && request.tools.length > 0 && this.chatModel.bindTools
            ? this.chatModel.bindTools(this.tools)
            : this.chatModel;
        const stream = await modelWithTools.stream(messages);
        return this.convertStreamToGenerateContentResponse(stream, request);
    }
    /**
     * Count tokens in the input
     */
    async countTokens(request) {
        try {
            const messages = this.convertContentsToMessages(request.contents);
            const text = messages.map(msg => msg.content).join(' ');
            // Estimate token count (rough approximation)
            const estimatedTokens = Math.ceil(text.length / 4);
            return {
                totalTokens: estimatedTokens
            };
        }
        catch (error) {
            throw new Error(`Token counting failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Generate embeddings for content
     */
    async embedContent(request) {
        try {
            const text = this.extractTextFromContent(request.content);
            const embeddings = await this.embeddings.embedQuery(text);
            return {
                embedding: {
                    values: embeddings
                }
            };
        }
        catch (error) {
            throw new Error(`Embedding generation failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Convert content array to LangChain messages
     */
    convertContentsToMessages(contents) {
        return contents.map(content => {
            const text = this.extractTextFromContent(content);
            if (content.role === 'model') {
                return new AIMessage(text);
            }
            else {
                return new HumanMessage(text);
            }
        });
    }
    /**
     * Extract text from content
     */
    extractTextFromContent(content) {
        return content.parts
            .filter((part) => 'text' in part)
            .map(part => part.text)
            .join('');
    }
    /**
     * Convert LangChain response to our format
     */
    convertToGenerateContentResponse(result, _request) {
        const content = {
            role: 'model',
            parts: [{ text: typeof result.content === 'string' ? result.content : String(result.content) }]
        };
        return {
            candidates: [{
                    content,
                    finishReason: 'STOP',
                    index: 0
                }],
            usageMetadata: {
                promptTokenCount: 0, // We don't have accurate token counting
                candidatesTokenCount: 0,
                totalTokenCount: 0
            }
        };
    }
    /**
     * Convert streaming response to our format
     */
    async *convertStreamToGenerateContentResponse(stream, _request) {
        let accumulatedContent = '';
        for await (const chunk of stream) {
            const chunkText = typeof chunk.content === 'string' ? chunk.content : String(chunk.content);
            accumulatedContent += chunkText;
            const content = {
                role: 'model',
                parts: [{ text: accumulatedContent }]
            };
            yield {
                candidates: [{
                        content,
                        finishReason: 'STOP',
                        index: 0
                    }],
                usageMetadata: {
                    promptTokenCount: 0,
                    candidatesTokenCount: 0,
                    totalTokenCount: 0
                }
            };
        }
    }
    /**
     * Update the chat model
     */
    updateChatModel(newChatModel) {
        this.chatModel = newChatModel;
    }
    /**
     * Update the embeddings model
     */
    updateEmbeddings(newEmbeddings) {
        this.embeddings = newEmbeddings;
    }
    /**
     * Update the tools
     */
    updateTools(newTools) {
        this.tools = newTools;
    }
    /**
     * Get model information
     */
    getModelInfo() {
        return {
            chatModel: this.chatModel.constructor.name,
            embeddings: this.embeddings.constructor.name,
            toolCount: this.tools.length
        };
    }
}
