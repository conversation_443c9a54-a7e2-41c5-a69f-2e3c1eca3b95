import { HT_MODELS } from './../config/models.js';
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Simplified agent without LangGraph dependency
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { Tool } from '@langchain/core/tools';
import {
  AIMessage,
  AIMessageChunk,
  HumanMessage,
  SystemMessage,
  BaseMessage,
  ToolMessage,
} from '@langchain/core/messages';
import { logger } from '../utils/logger.js';

import type { ToolCall } from '../types/index.js';
import type { LangChainConfig } from '../config/config.js';

/**
 * LangChain-based agent that manages conversation flow and tool execution
 */
export class LangChainAgent {
  private chatModel: BaseChatModel;
  private tools: Tool[];
  private systemPrompt: string;
  private config?: LangChainConfig;

  constructor(
    config: LangChainConfig,
    systemPrompt?: string
  ) {
    this.config = config;
    this.chatModel = config.chatModel;
    this.tools = config.tools;
    this.systemPrompt = systemPrompt || this.getDefaultSystemPrompt();
  }

  /**
   * Prepare messages for the chat model, including system prompt
   */
  private prepareMessages(messages: BaseMessage[], userMemory?: string): BaseMessage[] {
    const preparedMessages: BaseMessage[] = [];
    
    // Add system prompt
    if (this.systemPrompt) {
      let prompt = this.systemPrompt;
      if (userMemory) {
        prompt += `\n\nUser Memory:\n${userMemory}`;
      }
      preparedMessages.push(new SystemMessage(prompt));
    }
    
    // Add conversation messages
    preparedMessages.push(...messages);
    
    return preparedMessages;
  }

  /**
   * Execute tool calls and return the results
   */
  private async executeToolCalls(toolCalls: ToolCall[]): Promise<ToolMessage[]> {
    const toolMessages: ToolMessage[] = [];

    for (const toolCall of toolCalls) {
      const tool = this.tools.find(t => t.name === toolCall.name);
      if (!tool) {
        const errorMsg = `Tool not found: ${toolCall.name}`;
        toolMessages.push(
          new ToolMessage({
            content: errorMsg,
            tool_call_id: toolCall.id || '',
          })
        );
        continue;
      }

      try {
        const result = await tool.invoke(toolCall.args || {});
        toolMessages.push(
          new ToolMessage({
            content: result,
            tool_call_id: toolCall.id || '',
          })
        );
      } catch (toolError) {
        const errorMsg = `Tool execution failed: ${toolError instanceof Error ? toolError.message : String(toolError)}`;
        toolMessages.push(
          new ToolMessage({
            content: errorMsg,
            tool_call_id: toolCall.id || '',
          })
        );
      }
    }

    return toolMessages;
  }



  /**
   * Process a user message and return the response
   */
  async processMessage(
    userMessage: string,
    _sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): Promise<string> {
    try {
      logger.debug('[LangChainAgent] Processing message:', userMessage);
      
      // Prepare conversation messages
      const messages = [...conversationHistory, new HumanMessage(userMessage)];
      const preparedMessages = this.prepareMessages(messages, userMemory);
      
      // For OpenAI-compatible APIs, avoid binding tools to prevent Zod schema issues
      // Instead, we'll handle tool calls manually if needed
      const modelWithTools = this.chatModel;
      
      // Generate initial response with error handling
      let response;
      try {
        response = await modelWithTools.invoke(preparedMessages);
        
        // Validate response structure
        if (!response) {
          throw new Error('[LangChainAgent] Received null or undefined response from model');
        }
        
        // Check if response has expected structure
        if (typeof response === 'object' && 'content' in response) {
          // Response looks valid, continue
        } else {
          logger.warning('[LangChainAgent] Unexpected response structure:', response);
        }
      } catch (invokeError) {
        logger.error('[LangChainAgent] Model invoke error:', invokeError);
        
        // Handle specific error types
        if (invokeError instanceof Error) {
          if (invokeError.message.includes('Cannot read properties of undefined')) {
            // This is likely a malformed API response - try to provide a helpful fallback
            logger.warning('[LangChainAgent] API returned malformed response, attempting fallback...');
            
            // Try a simple text-only request without tools
            try {
              // TODO: remove this
              const simpleModel = new (await import('@langchain/openai')).ChatOpenAI({
                modelName: HT_MODELS[0], // 'gpt-3.5-turbo', // Use a more compatible model
                apiKey: process.env.OPENAI_API_KEY || 'dummy-key',
                temperature: 0.7,
                maxTokens: 1000,
                configuration: {
                  baseURL: process.env.OPENAI_BASE_URL,
                },
              });
              
              response = await simpleModel.invoke(preparedMessages);
            } catch (_fallbackError) {
              throw new Error('API endpoint appears to be incompatible with OpenAI format. Please verify your OPENAI_BASE_URL points to an OpenAI-compatible API.');
            }
          } else if (invokeError.message.includes('API key')) {
            throw new Error('API authentication failed. Please check your OPENAI_API_KEY.');
          } else if (invokeError.message.includes('network') || invokeError.message.includes('fetch')) {
            throw new Error('Network error occurred. Please check your connection and OPENAI_BASE_URL.');
          } else if (invokeError.message.includes('404')) {
            throw new Error('API endpoint not found. Please verify your OPENAI_BASE_URL.');
          } else if (invokeError.message.includes('401')) {
            throw new Error('Unauthorized. Please check your OPENAI_API_KEY.');
          }
        }
        throw invokeError;
      }
      
      // Handle tool calls if present
      let maxIterations = 5; // Prevent infinite loops
      while (response instanceof AIMessage && response.tool_calls?.length && maxIterations > 0) {
        logger.debug('[LangChainAgent] Executing tool calls:', response.tool_calls.map(tc => tc.name));
        
        // Convert LangChain ToolCall to our ToolCall format
        const convertedToolCalls: ToolCall[] = response.tool_calls.map(tc => ({
          id: tc.id || `call_${Date.now()}`,
          name: tc.name,
          args: tc.args
        }));
        // Execute tool calls
        const toolMessages = await this.executeToolCalls(convertedToolCalls);
        
        // Add tool response to conversation
        const updatedMessages = [...preparedMessages, response, ...toolMessages];
        
        // Generate follow-up response
        response = await modelWithTools.invoke(updatedMessages);
        maxIterations--;
      }
      
      // Extract final response
      if (response instanceof AIMessage || response instanceof AIMessageChunk) {
        if (typeof response.content === 'string') {
          const content = response.content || 'Empty response';
          // Process thinking tags for non-streaming response
          return this.processThinkingTags(content);
        } else if (response.content !== null && response.content !== undefined) {
          return JSON.stringify(response.content);
        } else {
          logger.warning('[LangChainAgent] Response content is null/undefined');
          return 'No content in response';
        }
      }
      
      // Handle non-AIMessage responses (API compatibility issue)
      logger.warning('[LangChainAgent] Response is not an AIMessage or AIMessageChunk:', typeof response);
      logger.debug('[LangChainAgent] Response structure:', response);
      
      // Try to extract content from various possible response formats
      if (response && typeof response === 'object') {
        const responseObj = response as Record<string, unknown>; // Type assertion to handle unknown response formats
        
        // Check for OpenAI-style response
        if ('choices' in responseObj && Array.isArray(responseObj.choices) && responseObj.choices[0]) {
          const choice = responseObj.choices[0];
          if (choice.message && choice.message.content) {
            return choice.message.content;
          }
        }
        
        // Check for direct content property
        if ('content' in responseObj && typeof responseObj.content === 'string') {
          return responseObj.content;
        }
        
        // Check for message property
        if ('message' in responseObj && responseObj.message && typeof responseObj.message === 'string') {
          return responseObj.message;
        }
        
        // Last resort: stringify the response
        return JSON.stringify(responseObj);
      }
      
      return 'No response generated';
    } catch (error) {
      logger.error('[LangChainAgent] Error processing message:', error);
      throw new Error(`Agent processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Stream responses for real-time interaction with DeepSeek R1 thinking support
   */
  async *streamMessage(
    userMessage: string,
    _sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): AsyncGenerator<string> {
    try {
      logger.debug('[LangChainAgent] Streaming message:', userMessage);
      
      // Prepare conversation messages
      const messages = [...conversationHistory, new HumanMessage(userMessage)];
      const preparedMessages = this.prepareMessages(messages, userMemory);
      
      // For OpenAI-compatible APIs, avoid binding tools to prevent Zod schema issues
      const modelWithTools = this.chatModel;
      
      // Stream initial response
      const stream = await modelWithTools.stream(preparedMessages);
      
      let buffer = '';
      let inThinkingMode = false;
      let thinkingContent = '';
      
      for await (const chunk of stream) {
        if ((chunk instanceof AIMessage || chunk instanceof AIMessageChunk) && typeof chunk.content === 'string') {
          buffer += chunk.content;
          
          // Process the buffer to handle thinking tags
          while (buffer.length > 0) {
            if (!inThinkingMode) {
              // Look for start of thinking
              const thinkStart = buffer.indexOf('<think>');
              if (thinkStart !== -1) {
                // Yield any content before thinking
                if (thinkStart > 0) {
                  yield buffer.substring(0, thinkStart);
                }
                buffer = buffer.substring(thinkStart + 7); // Remove '<think>'
                inThinkingMode = true;
                thinkingContent = '';
                yield '\n🤔 思考中...\n';
              } else {
                // No thinking tag found, check if we might get one soon
                const possibleStart = buffer.lastIndexOf('<');
                if (possibleStart !== -1 && possibleStart > buffer.length - 10) {
                  // Keep potential start of tag in buffer
                  if (possibleStart > 0) {
                    yield buffer.substring(0, possibleStart);
                  }
                  buffer = buffer.substring(possibleStart);
                  break;
                } else {
                  // Yield all content
                  yield buffer;
                  buffer = '';
                  break;
                }
              }
            } else {
              // In thinking mode, look for end tag
              const thinkEnd = buffer.indexOf('</think>');
              if (thinkEnd !== -1) {
                // Add to thinking content
                thinkingContent += buffer.substring(0, thinkEnd);
                buffer = buffer.substring(thinkEnd + 8); // Remove '</think>'
                inThinkingMode = false;
                
                // Show thinking summary
                const thinkingSummary = this.summarizeThinking(thinkingContent);
                yield `💭 ${thinkingSummary}\n\n`;
                thinkingContent = '';
              } else {
                // Keep accumulating thinking content
                thinkingContent += buffer;
                buffer = '';
                break;
              }
            }
          }
        }
      }
      
      // Handle any remaining buffer
      if (buffer.length > 0) {
        if (inThinkingMode) {
          thinkingContent += buffer;
          const thinkingSummary = this.summarizeThinking(thinkingContent);
          yield `💭 ${thinkingSummary}\n`;
        } else {
          yield buffer;
        }
      }
      
    } catch (error) {
      logger.error('[LangChainAgent] Error streaming message:', error);
      yield `Error: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Process thinking tags in response content
   */
  private processThinkingTags(content: string): string {
    // Remove thinking tags and replace with summary
    const thinkingRegex = /<think>([\s\S]*?)<\/think>/g;
    
    return content.replace(thinkingRegex, (_match, thinkingContent) => {
      const summary = this.summarizeThinking(thinkingContent);
      return `\n💭 思考过程: ${summary}\n\n`;
    });
  }

  /**
   * Summarize thinking content for display
   */
  private summarizeThinking(thinkingContent: string): string {
    if (!thinkingContent.trim()) {
      return '正在思考...';
    }
    
    // Extract key points from thinking
    const lines = thinkingContent.split('\n').filter(line => line.trim());
    if (lines.length === 0) {
      return '正在思考...';
    }
    
    // Take first meaningful line as summary
    const firstLine = lines[0].trim();
    if (firstLine.length > 100) {
      return firstLine.substring(0, 97) + '...';
    }
    
    return firstLine;
  }

  /**
   * Get the default system prompt
   */
  private getDefaultSystemPrompt(): string {
    return `You are an interactive CLI agent specializing in software engineering tasks. 
Your primary goal is to help users safely and efficiently.

You have access to various tools for file operations, web search, and code analysis.
Always use the appropriate tools to gather information before making changes.
Be concise and direct in your responses.`;
  }

  /**
   * Update the system prompt
   */
  updateSystemPrompt(prompt: string): void {
    this.systemPrompt = prompt;
  }

  /**
   * Update the model configuration
   */
  updateConfig(config: LangChainConfig): void {
    this.config = config;
    this.chatModel = config.chatModel;
    this.tools = config.tools;
  }

  /**
   * Get current configuration info
   */
  getConfigInfo(): {
    modelName: string;
    toolCount: number;
    hasSystemPrompt: boolean;
  } {
    return {
      modelName: this.chatModel.constructor.name,
      toolCount: this.tools.length,
      hasSystemPrompt: !!this.systemPrompt,
    };
  }
}