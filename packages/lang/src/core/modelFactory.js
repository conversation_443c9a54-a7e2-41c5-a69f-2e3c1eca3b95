/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';
import { ChatVertexAI, VertexAIEmbeddings } from '@langchain/google-vertexai';
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import logger from '../utils/logger.js';
import { AuthType, ExtendedAuthType } from '../types/index.js';
import { DEFAULT_LOCATION } from '../config/constants.js';
import { GEMINI_MODELS, HT_MODELS, OPENAI_MODELS, ANTHROPIC_MODELS, HT_EMBEDDING_MODEL, HT_API_KEY } from '../config/models.js';
/**
 * Factory function to create appropriate chat model based on auth type and model name
 */
export async function createChatModel(modelName, authType, apiKey, projectId, location = DEFAULT_LOCATION, baseURL) {
    const temperature = 0.7;
    const maxOutputTokens = 4096;
    switch (authType) {
        case AuthType.USE_GEMINI:
            return new ChatGoogleGenerativeAI({
                modelName: modelName.startsWith('gemini-') ? modelName : `gemini-${modelName}`,
                apiKey: apiKey || process.env.GEMINI_API_KEY,
                temperature,
                maxOutputTokens,
                streaming: true,
            });
        case AuthType.USE_VERTEX_AI:
            return new ChatVertexAI({
                model: modelName.startsWith('gemini-') ? modelName : `gemini-${modelName}`,
                location,
                temperature,
                maxOutputTokens,
                streaming: true,
            });
        case AuthType.LOGIN_WITH_GOOGLE:
        case AuthType.CLOUD_SHELL:
            // For OAuth and Cloud Shell, use Vertex AI with default credentials
            return new ChatVertexAI({
                model: modelName.startsWith('gemini-') ? modelName : `gemini-${modelName}`,
                location,
                temperature,
                maxOutputTokens,
                streaming: true,
            });
        case ExtendedAuthType.USE_OPENAI_COMPATIBLE: {
            const openaiConfig = {
                modelName,
                apiKey: apiKey || process.env.OPENAI_API_KEY || HT_API_KEY,
                temperature,
                maxTokens: maxOutputTokens,
                streaming: true,
                configuration: {
                    baseURL: baseURL || process.env.OPENAI_BASE_URL,
                    // Disable telemetry and analytics to prevent network issues
                    dangerouslyAllowBrowser: false,
                    // Add timeout configuration
                    timeout: 30000, // 30 seconds
                },
                // Disable structured outputs to avoid Zod compatibility issues
                strictTools: false,
                // Add timeout and retry configuration
                timeout: 30000, // 30 seconds
                maxRetries: 1, // Reduce retries to fail faster
                // Disable token counting for unknown models to prevent tiktoken errors
                modelKwargs: {
                // This helps avoid token counting issues with custom models
                },
            };
            try {
                const model = new ChatOpenAI(openaiConfig);
                // Override token counting methods to prevent "Unknown model" errors
                const originalGetNumTokens = model.getNumTokens.bind(model);
                const originalGetNumTokensFromMessages = model.getNumTokensFromMessages.bind(model);
                // Map custom model names to known tiktoken models for token counting
                const getTokenCountingModel = (modelName) => {
                    // Map custom models to similar OpenAI models for token counting
                    if (modelName.includes('deepseek') || modelName.includes('r1')) {
                        return 'gpt-4'; // Use GPT-4 tokenizer for DeepSeek models
                    }
                    if (modelName.includes('claude')) {
                        return 'gpt-4'; // Use GPT-4 tokenizer for Claude-like models
                    }
                    if (modelName.includes('gemini')) {
                        return 'gpt-3.5-turbo'; // Use GPT-3.5 tokenizer for Gemini-like models
                    }
                    // Default to GPT-3.5-turbo for unknown models
                    return 'gpt-3.5-turbo';
                };
                // Override getNumTokens to use a compatible model name
                model.getNumTokens = async function (text) {
                    try {
                        const originalModelName = this.modelName;
                        this.modelName = getTokenCountingModel(originalModelName);
                        const result = await originalGetNumTokens(text);
                        this.modelName = originalModelName;
                        return result;
                    }
                    catch (_error) {
                        // Fallback to character-based estimation
                        logger.debug('[ModelFactory] Token counting failed, using character-based estimation');
                        return Math.ceil(text.length / 4);
                    }
                };
                // Override getNumTokensFromMessages
                model.getNumTokensFromMessages = async function (messages) {
                    try {
                        const originalModelName = this.modelName;
                        this.modelName = getTokenCountingModel(originalModelName);
                        const result = await originalGetNumTokensFromMessages(messages);
                        this.modelName = originalModelName;
                        return result;
                    }
                    catch (_error) {
                        // Fallback to character-based estimation
                        logger.debug('[ModelFactory] Token counting from messages failed, using character-based estimation');
                        const totalChars = messages.reduce((sum, msg) => {
                            const msgObj = msg;
                            const content = typeof msgObj?.content === 'string' ? msgObj.content : JSON.stringify(msgObj?.content || '');
                            return sum + content.length;
                        }, 0);
                        const estimatedTokens = Math.ceil(totalChars / 4);
                        return {
                            totalCount: estimatedTokens,
                            countPerMessage: messages.map(() => Math.ceil(estimatedTokens / messages.length))
                        };
                    }
                };
                return model;
            }
            catch (error) {
                logger.error('[ModelFactory] Failed to create ChatOpenAI:', error);
                throw new Error(`Failed to create OpenAI-compatible model: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        case ExtendedAuthType.USE_ANTHROPIC:
            return new ChatAnthropic({
                model: modelName,
                apiKey: apiKey || process.env.ANTHROPIC_API_KEY,
                temperature,
                maxTokens: maxOutputTokens,
                streaming: true,
            });
        default:
            throw new Error(`Unsupported auth type: ${authType}`);
    }
}
/**
 * Factory function to create appropriate embeddings model based on auth type
 */
export async function createEmbeddings(modelName, authType, apiKey, projectId, location = DEFAULT_LOCATION, baseURL) {
    switch (authType) {
        case AuthType.USE_GEMINI:
            return new GoogleGenerativeAIEmbeddings({
                modelName: modelName.startsWith('text-embedding') ? modelName : 'text-embedding-004',
                apiKey: apiKey || process.env.GEMINI_API_KEY,
            });
        case AuthType.USE_VERTEX_AI:
            return new VertexAIEmbeddings({
                model: modelName.startsWith('text-embedding') ? modelName : 'text-embedding-004',
                location,
            });
        case AuthType.LOGIN_WITH_GOOGLE:
        case AuthType.CLOUD_SHELL:
            return new VertexAIEmbeddings({
                model: modelName.startsWith('text-embedding') ? modelName : 'text-embedding-004',
                location,
            });
        case ExtendedAuthType.USE_OPENAI_COMPATIBLE:
            return new OpenAIEmbeddings({
                model: modelName.startsWith('text-embedding') ? modelName : HT_EMBEDDING_MODEL,
                apiKey: apiKey || process.env.OPENAI_API_KEY,
                configuration: {
                    baseURL: baseURL || process.env.OPENAI_BASE_URL,
                },
            });
        case ExtendedAuthType.USE_ANTHROPIC:
            // Anthropic doesn't have embeddings, fall back to OpenAI
            return new OpenAIEmbeddings({
                model: 'text-embedding-3-small',
                apiKey: process.env.OPENAI_API_KEY,
            });
        default:
            throw new Error(`Unsupported auth type for embeddings: ${authType}`);
    }
}
/**
 * @deprecated
 * Get model display name for logging/UI purposes - GEMINI
 */
export function getModelDisplayName(modelName) {
    if (modelName.startsWith('gemini-')) {
        return modelName;
    }
    return `gemini-${modelName}`;
}
/**
 * @deprecated
 * Validate model name and suggest corrections if needed
 */
export function validateModelName(modelName) {
    const validModels = [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-1.0-pro',
        'gemini-pro',
        'gemini-flash',
    ];
    const normalizedName = modelName.toLowerCase();
    if (validModels.includes(normalizedName)) {
        return { isValid: true };
    }
    // Check if it's a partial match
    const suggestion = validModels.find(model => model.includes(normalizedName) || normalizedName.includes(model.replace('gemini-', '')));
    return {
        isValid: false,
        suggestion: suggestion || 'gemini-1.5-flash'
    };
}
/**
 * @deprecated
 * Get default model for a given auth type
 */
export function getDefaultModel(authType) {
    switch (authType) {
        case AuthType.USE_GEMINI:
        case AuthType.USE_VERTEX_AI:
        case AuthType.LOGIN_WITH_GOOGLE:
        case AuthType.CLOUD_SHELL:
            return 'gemini-1.5-flash';
        case ExtendedAuthType.USE_OPENAI_COMPATIBLE:
            return HT_MODELS[0]; // 'gpt-4o-mini';
        case ExtendedAuthType.USE_ANTHROPIC:
            return 'claude-3-5-sonnet-20241022';
        default:
            return 'gemini-1.5-flash';
    }
}
/**
 * Validate if a model name is compatible with the given auth type
 */
export function validateModelCompatibility(modelName, authType) {
    switch (authType) {
        case AuthType.USE_GEMINI:
        case AuthType.USE_VERTEX_AI:
        case AuthType.LOGIN_WITH_GOOGLE:
        case AuthType.CLOUD_SHELL:
            if (GEMINI_MODELS.some(model => modelName.includes(model.replace('gemini-', '')))) {
                return { isValid: true };
            }
            return { isValid: false, suggestion: 'gemini-1.5-flash' };
        case ExtendedAuthType.USE_OPENAI_COMPATIBLE:
            if (OPENAI_MODELS.some(model => modelName.includes(model))) {
                return { isValid: true };
            }
            return { isValid: false, suggestion: HT_MODELS[0] }; // 'gpt-4o-mini'
        case ExtendedAuthType.USE_ANTHROPIC:
            if (ANTHROPIC_MODELS.some(model => modelName.includes(model))) {
                return { isValid: true };
            }
            return { isValid: false, suggestion: 'claude-3-5-sonnet-20241022' };
        default:
            return { isValid: false, suggestion: HT_MODELS[0] }; // 'gemini-1.5-flash'
    }
}
