/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * 彩色日志工具
 * 提供统一的日志输出格式和颜色支持
 */
import chalk from 'chalk';
/**
 * 彩色日志函数
 */
export const logger = {
    info: (message, ...args) => console.log(chalk.blue(message), ...args),
    success: (message, ...args) => console.log(chalk.green(message), ...args),
    warning: (message, ...args) => console.log(chalk.yellow(message), ...args),
    error: (message, ...args) => console.error(chalk.red(message), ...args),
    debug: (message, ...args) => console.debug(chalk.gray(message), ...args),
};
/**
 * 默认导出日志工具
 */
export default logger;
