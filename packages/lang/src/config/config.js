/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { AuthType, ApprovalMode, FileDiscoveryService, GitService, } from '@google/gemini-cli-core';
import { LangChainContentGenerator } from '../core/contentGenerator.js';
import { LangChainToolRegistry } from '../tools/toolRegistry.js';
import { createChatModel, createEmbeddings, validateModelCompatibility } from '../core/modelFactory.js';
import { DEFAULT_LOCATION } from './constants.js';
import { ExtendedAuthType } from '../types/index.js';
import { HT_EMBEDDING_MODEL } from './models.js';
import { logger } from '../utils/logger.js';
/**
 * LangChain-based configuration manager that mirrors the functionality
 * of the core Config class but uses LangChain components internally.
 */
export class LangChainConfig {
    toolRegistry;
    sessionId;
    contentGeneratorConfig;
    embeddingModel;
    targetDir;
    debugMode;
    question;
    fullContext;
    coreTools;
    excludeTools;
    toolDiscoveryCommand;
    toolCallCommand;
    mcpServerCommand;
    mcpServers;
    userMemory;
    geminiMdFileCount;
    approvalMode;
    showMemoryUsage;
    accessibility;
    telemetrySettings;
    usageStatisticsEnabled;
    langChainClient;
    fileFiltering;
    fileDiscoveryService = null;
    gitService = undefined;
    checkpointing;
    proxy;
    cwd;
    bugCommand;
    model;
    extensionContextFilePaths;
    noBrowser;
    ideMode;
    modelSwitchedDuringSession = false;
    maxSessionTurns;
    listExtensions;
    _extensions;
    _blockedMcpServers;
    quotaErrorOccurred = false;
    summarizeToolOutput;
    experimentalAcp = false;
    // LangChain specific properties
    chatModel;
    embeddings;
    tools = [];
    temperature;
    maxTokens;
    streaming;
    baseURL;
    authTypeOverride;
    constructor(params) {
        this.sessionId = params.sessionId;
        this.embeddingModel = params.embeddingModel || HT_EMBEDDING_MODEL; // 'text-embedding-004';
        this.targetDir = params.targetDir;
        this.debugMode = params.debugMode;
        this.question = params.question;
        this.fullContext = params.fullContext ?? false;
        this.coreTools = params.coreTools;
        this.excludeTools = params.excludeTools;
        this.toolDiscoveryCommand = params.toolDiscoveryCommand;
        this.toolCallCommand = params.toolCallCommand;
        this.mcpServerCommand = params.mcpServerCommand;
        this.mcpServers = params.mcpServers;
        this.userMemory = params.userMemory || '';
        this.geminiMdFileCount = params.geminiMdFileCount || 50;
        this.approvalMode = params.approvalMode || ApprovalMode.DEFAULT;
        this.showMemoryUsage = params.showMemoryUsage ?? false;
        this.accessibility = params.accessibility || {};
        this.telemetrySettings = params.telemetry || {};
        this.usageStatisticsEnabled = params.usageStatisticsEnabled ?? true;
        this.fileFiltering = {
            respectGitIgnore: params.fileFiltering?.respectGitIgnore ?? true,
            respectGeminiIgnore: params.fileFiltering?.respectGeminiIgnore ?? true,
            enableRecursiveFileSearch: params.fileFiltering?.enableRecursiveFileSearch ?? true,
        };
        this.checkpointing = params.checkpointing ?? false;
        this.proxy = params.proxy;
        this.cwd = params.cwd;
        this.bugCommand = params.bugCommand;
        this.model = params.model;
        this.extensionContextFilePaths = params.extensionContextFilePaths || [];
        this.maxSessionTurns = params.maxSessionTurns || 100;
        this.listExtensions = params.listExtensions ?? false;
        this._extensions = params.extensions || [];
        this._blockedMcpServers = params.blockedMcpServers || [];
        this.noBrowser = params.noBrowser ?? false;
        this.summarizeToolOutput = params.summarizeToolOutput;
        this.ideMode = params.ideMode ?? false;
        this.baseURL = params.baseURL;
        this.authTypeOverride = params.authType;
        if (params.fileDiscoveryService) {
            this.fileDiscoveryService = params.fileDiscoveryService;
        }
    }
    async initialize() {
        const authType = this.getAuthType();
        // Validate model compatibility
        const validation = validateModelCompatibility(this.model, authType);
        if (!validation.isValid && validation.suggestion) {
            logger.warning(`Model ${this.model} may not be compatible with auth type ${authType}. Consider using ${validation.suggestion}`);
        }
        // Initialize LangChain components
        this.chatModel = await createChatModel(this.model, authType, process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY, process.env.GOOGLE_CLOUD_PROJECT, DEFAULT_LOCATION, this.baseURL);
        this.embeddings = await createEmbeddings(this.embeddingModel, authType, process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY, process.env.GOOGLE_CLOUD_PROJECT, DEFAULT_LOCATION, this.baseURL);
        // Initialize tool registry
        this.toolRegistry = new LangChainToolRegistry(this);
        await this.toolRegistry.discoverTools();
        this.tools = this.toolRegistry.getLangChainTools();
        // Initialize content generator
        this.langChainClient = new LangChainContentGenerator(this.chatModel, this.embeddings, this.tools);
    }
    getAuthType() {
        // Use override if provided
        if (this.authTypeOverride) {
            return this.authTypeOverride;
        }
        // Determine auth type from environment or config
        if (process.env.ANTHROPIC_API_KEY) {
            return ExtendedAuthType.USE_ANTHROPIC;
        }
        if (process.env.OPENAI_API_KEY || process.env.OPENAI_BASE_URL) {
            return ExtendedAuthType.USE_OPENAI_COMPATIBLE;
        }
        if (process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true') {
            return AuthType.USE_VERTEX_AI;
        }
        if (process.env.GEMINI_API_KEY) {
            return AuthType.USE_GEMINI;
        }
        return AuthType.LOGIN_WITH_GOOGLE;
    }
    async refreshAuth(authMethod) {
        // Recreate chat model with new auth
        this.chatModel = await createChatModel(this.model, authMethod, process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY, process.env.GOOGLE_CLOUD_PROJECT, DEFAULT_LOCATION, this.baseURL);
        this.embeddings = await createEmbeddings(this.embeddingModel, authMethod, process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY, process.env.GOOGLE_CLOUD_PROJECT, DEFAULT_LOCATION, this.baseURL);
        // Update content generator
        this.langChainClient = new LangChainContentGenerator(this.chatModel, this.embeddings, this.tools);
    }
    // Getters that mirror the core Config class
    getSessionId() {
        return this.sessionId;
    }
    getModel() {
        return this.model;
    }
    async setModel(newModel) {
        if (newModel === this.model) {
            return; // No change needed
        }
        // Validate the new model is compatible
        try {
            const authType = this.getAuthType();
            const validation = validateModelCompatibility(newModel, authType);
            if (!validation.isValid && validation.suggestion) {
                logger.warning(`Model ${newModel} may not be compatible with auth type ${authType}. Consider using ${validation.suggestion}`);
            }
            // Recreate chat model with new model name
            this.chatModel = await createChatModel(newModel, authType, process.env.GEMINI_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY, process.env.GOOGLE_CLOUD_PROJECT, DEFAULT_LOCATION, this.baseURL);
            // Update content generator with new model
            this.langChainClient = new LangChainContentGenerator(this.chatModel, this.embeddings, this.tools);
            // Update the model property and mark as switched
            this.model = newModel;
            this.modelSwitchedDuringSession = true;
            logger.debug(`[LangChainConfig] Model switched to: ${newModel}`);
        }
        catch (error) {
            logger.error(`[LangChainConfig] Failed to switch model to ${newModel}:`, error);
            throw new Error(`Failed to switch model: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    isModelSwitchedDuringSession() {
        return this.modelSwitchedDuringSession;
    }
    getMaxSessionTurns() {
        return this.maxSessionTurns;
    }
    setQuotaErrorOccurred(value) {
        this.quotaErrorOccurred = value;
    }
    getQuotaErrorOccurred() {
        return this.quotaErrorOccurred;
    }
    getEmbeddingModel() {
        return this.embeddingModel;
    }
    getTargetDir() {
        return this.targetDir;
    }
    getProjectRoot() {
        return this.targetDir;
    }
    async getToolRegistry() {
        return this.toolRegistry;
    }
    getDebugMode() {
        return this.debugMode;
    }
    getQuestion() {
        return this.question;
    }
    getFullContext() {
        return this.fullContext;
    }
    getCoreTools() {
        return this.coreTools;
    }
    getExcludeTools() {
        return this.excludeTools;
    }
    getToolDiscoveryCommand() {
        return this.toolDiscoveryCommand;
    }
    getToolCallCommand() {
        return this.toolCallCommand;
    }
    getMcpServerCommand() {
        return this.mcpServerCommand;
    }
    getMcpServers() {
        return this.mcpServers;
    }
    getUserMemory() {
        return this.userMemory;
    }
    setUserMemory(newUserMemory) {
        this.userMemory = newUserMemory;
    }
    getGeminiMdFileCount() {
        return this.geminiMdFileCount;
    }
    setGeminiMdFileCount(count) {
        this.geminiMdFileCount = count;
    }
    getApprovalMode() {
        return this.approvalMode;
    }
    setApprovalMode(mode) {
        this.approvalMode = mode;
    }
    getShowMemoryUsage() {
        return this.showMemoryUsage;
    }
    getAccessibility() {
        return this.accessibility;
    }
    getTelemetryEnabled() {
        return this.telemetrySettings.enabled ?? false;
    }
    getTelemetryLogPromptsEnabled() {
        return this.telemetrySettings.logPrompts ?? false;
    }
    getFileFilteringRespectGitIgnore() {
        return this.fileFiltering.respectGitIgnore;
    }
    getFileFilteringRespectGeminiIgnore() {
        return this.fileFiltering.respectGeminiIgnore;
    }
    getFileFilteringOptions() {
        return {
            respectGitIgnore: this.fileFiltering.respectGitIgnore,
            respectGeminiIgnore: this.fileFiltering.respectGeminiIgnore,
        };
    }
    getCheckpointingEnabled() {
        return this.checkpointing;
    }
    getProxy() {
        return this.proxy;
    }
    getWorkingDir() {
        return this.cwd;
    }
    getBugCommand() {
        return this.bugCommand;
    }
    getUsageStatisticsEnabled() {
        return this.usageStatisticsEnabled;
    }
    getExtensionContextFilePaths() {
        return this.extensionContextFilePaths;
    }
    getExperimentalAcp() {
        return this.experimentalAcp;
    }
    getListExtensions() {
        return this.listExtensions;
    }
    getExtensions() {
        return this._extensions;
    }
    getBlockedMcpServers() {
        return this._blockedMcpServers;
    }
    getNoBrowser() {
        return this.noBrowser;
    }
    isBrowserLaunchSuppressed() {
        return this.noBrowser;
    }
    getSummarizeToolOutputConfig() {
        return this.summarizeToolOutput;
    }
    getIdeMode() {
        return this.ideMode;
    }
    getFileService() {
        if (!this.fileDiscoveryService) {
            this.fileDiscoveryService = new FileDiscoveryService(this.getProjectRoot());
        }
        return this.fileDiscoveryService;
    }
    async getGitService() {
        if (!this.gitService) {
            this.gitService = new GitService(this.getProjectRoot());
            await this.gitService.initialize();
        }
        return this.gitService;
    }
    // LangChain specific getters
    getChatModel() {
        return this.chatModel;
    }
    getEmbeddings() {
        return this.embeddings;
    }
    getTools() {
        return this.tools;
    }
    getLangChainClient() {
        return this.langChainClient;
    }
    getLangChainConfig() {
        return {
            chatModel: this.chatModel,
            embeddings: this.embeddings,
            tools: this.tools,
            temperature: 0.7,
            maxTokens: 4096,
            streaming: true,
        };
    }
}
