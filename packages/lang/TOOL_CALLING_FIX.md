# 工具调用逻辑修复说明

## 问题分析

在检查packages/lang中的工具调用逻辑时，发现了以下关键问题：

### 1. 主要问题：LangChainAgent没有绑定工具给LLM

**问题位置**: `packages/lang/src/core/agent.ts` 第123-125行

**原始代码**:
```typescript
// For OpenAI-compatible APIs, avoid binding tools to prevent Zod schema issues
// Instead, we'll handle tool calls manually if needed
const modelWithTools = this.chatModel;
```

**问题**: 注释说明避免绑定工具以防止Zod schema问题，但这导致LLM无法获得工具信息，因此无法进行工具调用。

### 2. StateGraphAgent正确实现了工具绑定，但默认没有使用

**正确实现位置**: `packages/lang/src/core/stateGraphAgent.ts` 第167-170行

```typescript
// Bind tools to the model if available
const modelWithTools = this.tools.length > 0 && this.chatModel.bindTools
  ? this.chatModel.bindTools(this.tools)
  : this.chatModel;
```

**问题**: 虽然StateGraphAgent正确实现了工具绑定，但在`packages/lang/src/index.ts`中默认使用的是LangChainAgent。

### 3. 系统提示中缺少工具信息

**问题**: 即使绑定了工具，系统提示中也没有告知LLM有哪些工具可用。

## 解决方案

### 1. 修复LangChainAgent的工具绑定

**修改文件**: `packages/lang/src/core/agent.ts`

**修改内容**:
- 在`processMessage`方法中正确绑定工具
- 在`streamMessage`方法中正确绑定工具
- 在系统提示中添加工具信息

**关键修改**:
```typescript
// 修复工具绑定
const modelWithTools = this.tools.length > 0 && this.chatModel.bindTools
  ? this.chatModel.bindTools(this.tools)
  : this.chatModel;

// 在系统提示中添加工具信息
if (this.tools.length > 0) {
  prompt += `\n\n# Available Tools\n\nYou have access to the following tools:\n`;
  for (const tool of this.tools) {
    prompt += `- **${tool.name}**: ${tool.description}\n`;
  }
  prompt += `\nUse these tools when needed to help the user.`;
}
```

### 2. 重写streamMessage方法支持工具调用

**问题**: 原始的streamMessage方法只处理文本流，不支持工具调用。

**解决方案**: 重写方法以支持：
- 检测工具调用
- 执行工具
- 将工具结果传回LLM
- 继续对话流程

### 3. 添加StateGraphAgent选项

**修改文件**: `packages/lang/src/index.ts`

**新增功能**:
- 添加`useStateGraph`参数选择使用StateGraphAgent
- 导出StateGraphAgent供直接使用

```typescript
export async function createLangChainGeminiCLI(params: ConfigParameters & {
  useStateGraph?: boolean; // 新增选项
}) {
  // 根据选项创建不同的agent
  if (params.useStateGraph) {
    const { StateGraphAgent } = await import('./core/stateGraphAgent.js');
    agent = new StateGraphAgent(config, getCoreSystemPrompt(), { enablePersistence: true });
  } else {
    const { LangChainAgent } = await import('./core/agent.js');
    agent = new LangChainAgent(config, getCoreSystemPrompt());
  }
}
```

## 修复后的工具调用流程

### 1. 用户提示时的信息传递

1. **系统提示包含工具信息**: LLM知道有哪些工具可用
2. **工具绑定到模型**: LLM可以生成工具调用
3. **完整的对话上下文**: 包括历史消息和用户记忆

### 2. LLM工具调用流程

1. **LLM分析用户请求**: 确定是否需要使用工具
2. **生成工具调用**: 如果需要，生成适当的工具调用
3. **工具执行**: Agent执行工具并获取结果
4. **结果传回LLM**: 工具结果作为消息传回LLM
5. **生成最终响应**: LLM基于工具结果生成回答

### 3. 错误处理和安全性

- **工具不存在**: 返回错误消息给LLM
- **工具执行失败**: 捕获异常并返回错误信息
- **无限循环防护**: 限制最大工具调用次数
- **类型安全**: 正确的类型转换和验证

## 测试验证

创建了测试脚本 `test-tool-calling.ts` 来验证：

1. **LangChainAgent工具调用**: 测试修复后的LangChainAgent
2. **StateGraphAgent工具调用**: 测试StateGraphAgent的工具调用
3. **工具信息显示**: 验证工具正确注册和显示
4. **实际工具执行**: 测试文件操作等工具的实际执行

## 使用示例

```typescript
// 使用修复后的LangChainAgent (默认)
const cli = await createLangChainGeminiCLI({
  sessionId: 'session-1',
  model: 'ht::saas-deepseek-v3',
  targetDir: process.cwd(),
  cwd: process.cwd(),
  coreTools: ['list_directory', 'read_file', 'search_file_content'],
});

// 使用StateGraphAgent (推荐用于复杂工具调用)
const cliAdvanced = await createLangChainGeminiCLI({
  sessionId: 'session-2',
  model: 'ht::saas-deepseek-v3',
  targetDir: process.cwd(),
  cwd: process.cwd(),
  useStateGraph: true, // 启用StateGraphAgent
  coreTools: ['list_directory', 'read_file', 'search_file_content', 'replace'],
});
```

## 总结

通过这些修复，packages/lang现在具备了完整的工具调用功能：

1. ✅ **工具信息正确传递给LLM**
2. ✅ **LLM可以成功调用工具**
3. ✅ **工具执行结果正确返回给LLM**
4. ✅ **支持多轮工具调用对话**
5. ✅ **提供两种Agent选择**
6. ✅ **完善的错误处理和安全防护**

这确保了lang包的agent逻辑完整，能够正确处理用户请求并使用工具来完成任务。
